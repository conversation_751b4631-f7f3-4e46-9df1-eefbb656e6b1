"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Divider } from "@heroui/react";
import { useDiseaseData } from "../contexts/DiseaseDataContext";
import { useEffect, useState } from "react";
import { getAuth } from "firebase/auth";

export default function DebugDataStatus() {
  const { rawData, processedData, loading } = useDiseaseData();
  const [currentUser, setCurrentUser] = useState<string | null>(null);

  useEffect(() => {
    const auth = getAuth();
    const user = auth.currentUser;
    setCurrentUser(user?.email || null);
  }, []);

  return (
    <Card className="max-w-2xl mx-auto mb-6">
      <CardHeader className="flex gap-3 items-center text-black">
        <div className="flex flex-col">
          <h3 className="text-lg font-bold text-[#143D60]">🔍 Data Debug Status</h3>
          <p className="text-sm text-gray-600">Current user: {currentUser}</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-gray-700">Loading Status:</h4>
            <p className={`text-sm ${loading ? 'text-orange-600' : 'text-green-600'}`}>
              {loading ? '⏳ Loading data...' : '✅ Data loaded'}
            </p>
          </div>

          <div>
            <h4 className="font-semibold text-gray-700">Raw Data Count:</h4>
            <p className="text-sm text-blue-600">
              📊 {rawData.length} total records
            </p>
          </div>

          <div>
            <h4 className="font-semibold text-gray-700">Processed Diseases:</h4>
            <p className="text-sm text-purple-600">
              🦠 {Object.keys(processedData).length} unique diseases
            </p>
            {Object.keys(processedData).length > 0 && (
              <div className="mt-2 text-xs text-gray-600">
                <strong>Diseases:</strong> {Object.keys(processedData).join(', ')}
              </div>
            )}
          </div>

          <div>
            <h4 className="font-semibold text-gray-700">Municipalities with Data:</h4>
            {Object.keys(processedData).length > 0 ? (
              <div className="text-sm text-green-600">
                🏙️ {[...new Set(
                  Object.values(processedData).flatMap(disease => 
                    Object.keys(disease.municipalities)
                  )
                )].join(', ')}
              </div>
            ) : (
              <p className="text-sm text-red-600">❌ No municipality data available</p>
            )}
          </div>

          <div>
            <h4 className="font-semibold text-gray-700">Data Source:</h4>
            <p className="text-sm text-indigo-600">
              {rawData.length > 0 ? (
                rawData.some(item => 'uploadedBy' in item) ? 
                  '🌍 Global centralized data' : 
                  '👤 User-specific data (fallback)'
              ) : (
                '❌ No data source'
              )}
            </p>
          </div>

          {rawData.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-700">Sample Data:</h4>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                {JSON.stringify(rawData[0], null, 2)}
              </pre>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
}
