"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, CardBody, <PERSON><PERSON><PERSON><PERSON>, Divide<PERSON> } from "@heroui/react";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/firebase";
import { getAuth } from "firebase/auth";

export default function TestCentralizedData() {
  const [testResult, setTestResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testCentralizedAccess = async () => {
    setLoading(true);
    setTestResult("Testing...");
    
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
      setTestResult("❌ No user authenticated");
      setLoading(false);
      return;
    }

    try {
      console.log("🧪 Testing centralized collection access...");
      
      // Test centralized collection access
      const centralizedRef = collection(
        db,
        'healthradarDB',
        'centralizedData',
        'allCases'
      );

      const snapshot = await getDocs(centralizedRef);
      const data = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

      console.log("🧪 Centralized test result:", data);

      if (data.length > 0) {
        setTestResult(`✅ SUCCESS: Found ${data.length} records in centralized collection\n\nSample data:\n${JSON.stringify(data[0], null, 2)}`);
      } else {
        setTestResult("⚠️ Centralized collection exists but is EMPTY");
      }

    } catch (error) {
      console.error("🧪 Centralized test error:", error);
      setTestResult(`❌ ERROR accessing centralized collection:\n${error}`);
    }

    setLoading(false);
  };

  return (
    <Card className="max-w-2xl mx-auto mb-6">
      <CardHeader className="flex gap-3 items-center text-black">
        <div className="flex flex-col">
          <h3 className="text-lg font-bold text-[#143D60]">🧪 Test Centralized Data Access</h3>
          <p className="text-sm text-gray-600">Click to test if centralized collection is accessible</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody>
        <div className="space-y-4">
          <Button 
            onClick={testCentralizedAccess}
            disabled={loading}
            className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white"
          >
            {loading ? "Testing..." : "Test Centralized Access"}
          </Button>
          
          {testResult && (
            <div className="mt-4">
              <h4 className="font-semibold text-gray-700 mb-2">Test Result:</h4>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto whitespace-pre-wrap">
                {testResult}
              </pre>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
}
