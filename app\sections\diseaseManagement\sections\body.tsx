"use client";

import {
  <PERSON>,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from "@heroui/react";
import Papa from "papaparse";
import { getDocs, query, deleteDoc, doc, getDoc } from "firebase/firestore";
import { collection, addDoc } from "firebase/firestore";
import { db } from "@/firebase";

import { useEffect, useState, useCallback } from "react";
import { getAuth } from "firebase/auth";
import { useDiseaseData } from "../../../contexts/DiseaseDataContext";

interface CsvData {
  [key: string]: string | number;
}

export default function Body() {
  const [ArrayKeys, setArrayKeys] = useState<string[]>([]);
  const [ArrayValues, setArrayValues] = useState<CsvData[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const { refreshData } = useDiseaseData();

  // Municipality validation states
  const [userMunicipality, setUserMunicipality] = useState<string>("");
  const [validationError, setValidationError] = useState<{
    show: boolean;
    title: string;
    message: string;
    invalidMunicipalities: string[];
  }>({
    show: false,
    title: "",
    message: "",
    invalidMunicipalities: []
  });

  const auth = getAuth();
  const user = auth.currentUser;

  // 🏙️ Fetch user's assigned municipality
  const fetchUserMunicipality = async (): Promise<string | null> => {
    if (!user) return null;

    try {
      const userDocRef = doc(db, 'healthradarDB', 'users', 'healthworker', user.uid);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const municipality = userData.municipality;
        console.log(`👤 User's assigned municipality: ${municipality}`);
        return municipality;
      } else {
        console.error('❌ User document not found');
        return null;
      }
    } catch (error) {
      console.error('❌ Error fetching user municipality:', error);
      return null;
    }
  };

  // 🔍 Validate CSV data against user's municipality
  const validateMunicipalityData = (csvData: CsvData[], userMunicipality: string): {
    isValid: boolean;
    invalidMunicipalities: string[];
    invalidRecords: CsvData[];
  } => {
    const invalidMunicipalities = new Set<string>();
    const invalidRecords: CsvData[] = [];

    csvData.forEach((record) => {
      const recordMunicipality = record.Municipality?.toString().trim();

      if (recordMunicipality && recordMunicipality.toLowerCase() !== userMunicipality.toLowerCase()) {
        invalidMunicipalities.add(recordMunicipality);
        invalidRecords.push(record);
      }
    });

    return {
      isValid: invalidMunicipalities.size === 0,
      invalidMunicipalities: Array.from(invalidMunicipalities),
      invalidRecords
    };
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setLoading(true);
    const file = event.target.files?.[0];
    if (!file) {
      setLoading(false);
      return;
    }

    // Use Papa Parse para ma parse ang CSV - mas reliable ni kaysa manual parsing
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        console.log("Parsed Results:", results.data);
        const parsedData = results.data as CsvData[];

        if (parsedData.length === 0) {
          setValidationError({
            show: true,
            title: "Empty CSV File",
            message: "The uploaded CSV file is empty or contains no valid data.",
            invalidMunicipalities: []
          });
          setLoading(false);
          return;
        }

        console.log("Current User:", user?.email);

        if (!user) {
          console.error("User not authenticated.");
          setLoading(false);
          return;
        }

        // 🏙️ Fetch user's municipality and validate CSV data
        const userMunicipality = await fetchUserMunicipality();
        if (!userMunicipality) {
          setValidationError({
            show: true,
            title: "User Municipality Not Found",
            message: "Could not determine your assigned municipality. Please contact support.",
            invalidMunicipalities: []
          });
          setLoading(false);
          return;
        }

        // Store user municipality for display
        setUserMunicipality(userMunicipality);

        // 🔍 Validate that all CSV data belongs to user's municipality
        const validation = validateMunicipalityData(parsedData, userMunicipality);

        if (!validation.isValid) {
          setValidationError({
            show: true,
            title: "Municipality Mismatch",
            message: `You can only upload data for your assigned municipality: ${userMunicipality}. The uploaded file contains data for other municipalities.`,
            invalidMunicipalities: validation.invalidMunicipalities
          });
          setLoading(false);
          return;
        }

        // ✅ Validation passed, proceed with upload
        console.log(`✅ All data validated for municipality: ${userMunicipality}`);

        if (parsedData.length > 0) {
          const keys = Object.keys(parsedData[0]);
          setArrayKeys(keys);
          setArrayValues(parsedData);
        }

        try {
          console.log("🚀 Starting upload process for", parsedData.length, "records");

          // Upload tanan data sa Firebase - Promise.all para ma upload all at once
          await Promise.all(
            parsedData.map(async (disease) => {
              // 📝 Upload to user's personal collection
              await addDoc(
                collection(
                  db,
                  "healthradarDB",
                  "users",
                  "healthworker",
                  user.uid,
                  "UploadedCases"
                ),
                disease
              );

              // 🌍 Also upload to centralized collection for all users to see
              await addDoc(
                collection(
                  db,
                  "healthradarDB",
                  "centralizedData",
                  "allCases"
                ),
                {
                  ...disease,
                  uploadedBy: user.uid,
                  uploadedByEmail: user.email,
                  uploadedAt: new Date().toISOString()
                }
              );
            })
          );

          console.log("✅ All diseases uploaded successfully to both collections!");

          // Refresh ang data context para ma update ang charts - importante ni!
          await refreshData();

        } catch (error) {
          console.error("Error uploading documents to Firestore:", error);
        }
      },
    });
  };

  // 🟢 Fetch uploaded cases from Firebase
  const fetchUploadedCases = useCallback(async () => {
    if (!user) return;

    const casesQuery = collection(
      db,
      "healthradarDB",
      "users",
      "healthworker",
      user.uid,
      "UploadedCases"
    );

    const querySnapshot = await getDocs(query(casesQuery));

    const fetchedData: CsvData[] = [];
    querySnapshot.forEach((doc) => {
      fetchedData.push(doc.data() as CsvData);
    });

    if (fetchedData.length > 0) {
      const allKeys = [
        ...new Set(fetchedData.flatMap((doc) => Object.keys(doc))),
      ];
      setArrayKeys(allKeys);
      setArrayValues(fetchedData);
    }
    // if (fetchedData.length > 0) {
    //   setData(fetchedData);
    //   setArrayKeys(Object.keys(fetchedData[0]));
    //   setArrayValues(fetchedData.map(Object.values));
    //   // setArrayValues(fetchedData.map(Object.values));
    //   setTableLoading(true);
    // }
  }, [user]);

  // �️ Delete all uploaded cases
  const deleteAllCases = useCallback(async () => {
    if (!user) {
      console.log("No user authenticated - kinsa man ni wala'y login?");
      return;
    }

    setDeleteLoading(true);
    try {
      // 🗑️ Delete from user's personal collection
      const casesRef = collection(
        db,
        "healthradarDB",
        "users",
        "healthworker",
        user.uid,
        "UploadedCases"
      );

      const querySnapshot = await getDocs(query(casesRef));

      // Delete all documents from personal collection
      const deletePersonalPromises = querySnapshot.docs.map((document) =>
        deleteDoc(doc(db, "healthradarDB", "users", "healthworker", user.uid, "UploadedCases", document.id))
      );

      await Promise.all(deletePersonalPromises);

      // 🌍 Delete from centralized collection (only records uploaded by this user)
      const centralizedRef = collection(
        db,
        "healthradarDB",
        "centralizedData",
        "allCases"
      );

      const centralizedSnapshot = await getDocs(query(centralizedRef));

      // Filter and delete only documents uploaded by current user
      const deleteCentralizedPromises = centralizedSnapshot.docs
        .filter(doc => doc.data().uploadedBy === user.uid)
        .map(document =>
          deleteDoc(doc(db, "healthradarDB", "centralizedData", "allCases", document.id))
        );

      await Promise.all(deleteCentralizedPromises);

      const totalDeleted = querySnapshot.docs.length;
      console.log(`Deleted ${totalDeleted} records from both collections`);

      // Clear the local state
      setArrayKeys([]);
      setArrayValues([]);

      // Refresh the data context to update charts
      await refreshData();

      alert(`Successfully deleted ${totalDeleted} records from both personal and centralized collections`);
    } catch (error) {
      console.error("Error deleting cases:", error);
      alert("Error deleting data. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  }, [user, refreshData]);

  // �🔄 Fetch on component mount
  useEffect(() => {
    if (user) {
      fetchUploadedCases();
    }
  }, [user, fetchUploadedCases]);

  return (
    <div className="flex-1 overflow-auto">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-[#143D60] mb-2">Disease Management</h1>
            <p className="text-gray-600">Upload, manage, and analyze disease case data</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>{ArrayValues.length} records</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Action Bar */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-md">
              <Input
                placeholder="Search disease name..."
                className="w-full"
                classNames={{
                  input: "text-gray-900 placeholder:text-gray-400",
                  inputWrapper: "border-gray-200 hover:border-[#A0C878] focus-within:border-[#A0C878] bg-gray-50 hover:bg-white transition-colors"
                }}
                startContent={
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
                size="lg"
                radius="lg"
              />
            </div>

            <div className="flex gap-3">
              <Button
                onPress={onOpen}
                disabled={loading}
                className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                startContent={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                }
                size="lg"
                radius="lg"
              >
                Upload CSV
              </Button>

              <Button
                onPress={deleteAllCases}
                disabled={deleteLoading || ArrayValues.length === 0}
                className="bg-gradient-to-r from-red-500 to-red-600 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                startContent={
                  deleteLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  )
                }
                size="lg"
                radius="lg"
              >
                {deleteLoading ? "Deleting..." : "Delete All Data"}
              </Button>
            </div>
          </div>
        </div>
        {/* Data Table */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-[#A0C878] to-[#DDEB9D] rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-[#143D60]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H9a2 2 0 00-2 2v10z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-[#143D60]">Disease Case Data</h3>
                <p className="text-sm text-gray-600">Uploaded CSV data overview</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            {ArrayValues.length > 0 ? (
              <div className="overflow-x-auto">
                <Table
                  aria-label="Disease case data table"
                  classNames={{
                    wrapper: "shadow-none border border-gray-200 rounded-xl",
                    th: "bg-gray-50 text-[#143D60] font-semibold",
                    td: "text-gray-700"
                  }}
                >
                  <TableHeader>
                    {ArrayKeys.map((key: string) => (
                      <TableColumn key={key} className="text-center">
                        {key}
                      </TableColumn>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {ArrayValues.map((row: CsvData, index: number) => (
                      <TableRow key={index} className="hover:bg-gray-50">
                        {ArrayKeys.map((key: string, idx: number) => (
                          <TableCell key={idx} className="text-center">
                            {row[key] || ""}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">No Data Available</h3>
                <p className="text-gray-500 mb-4">Upload a CSV file to view disease case data</p>
                <Button
                  onPress={onOpen}
                  className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold"
                  startContent={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  }
                >
                  Upload CSV File
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Upload Modal */}
        <Modal isOpen={isOpen} onOpenChange={onOpenChange} size="lg">
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="text-xl font-bold text-[#143D60] border-b border-gray-200 pb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#143D60] to-[#1e4a6b] rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    Upload CSV File
                  </div>
                </ModalHeader>
                <ModalBody className="py-6">
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-gray-600 mb-4">Select a CSV file containing disease case data</p>
                    </div>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) => {
                        handleFileUpload(e);
                        onClose();
                      }}
                      className="w-full"
                      classNames={{
                        input: "text-gray-900",
                        inputWrapper: "border-2 border-dashed border-gray-300 hover:border-[#A0C878] bg-gray-50 hover:bg-white transition-colors h-20"
                      }}
                      size="lg"
                      radius="lg"
                    />
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">CSV Format Requirements:</h4>
                      <ul className="text-xs text-blue-700 space-y-1">
                        <li>• Include columns: Municipality, DiseaseName, CaseCount</li>
                        <li>• Use proper municipality names (e.g., &quot;Lilo-an&quot;, &quot;Mandaue&quot;, &quot;Consolacion&quot;)</li>
                        <li>• Ensure CaseCount contains numeric values</li>
                        <li>• <strong>Important:</strong> You can only upload data for your assigned municipality</li>
                      </ul>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter className="border-t border-gray-200 pt-4">
                  <Button
                    color="danger"
                    variant="light"
                    onPress={onClose}
                    className="font-semibold"
                  >
                    Cancel
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>

        {/* Municipality Validation Error Modal */}
        <Modal
          isOpen={validationError.show}
          onOpenChange={(open) => setValidationError(prev => ({ ...prev, show: open }))}
          size="lg"
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className="text-xl font-bold text-red-600 border-b border-gray-200 pb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    {validationError.title}
                  </div>
                </ModalHeader>
                <ModalBody className="py-6">
                  <div className="space-y-4">
                    <p className="text-gray-700">
                      {validationError.message}
                    </p>

                    {validationError.invalidMunicipalities.length > 0 && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h4 className="text-sm font-semibold text-red-800 mb-2">
                          Invalid municipalities found in your CSV:
                        </h4>
                        <ul className="text-sm text-red-700 space-y-1">
                          {validationError.invalidMunicipalities.map((municipality, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                              {municipality}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-blue-800 mb-2">
                        📋 What you need to do:
                      </h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Ensure all data in your CSV belongs to your assigned municipality</li>
                        <li>• Check the &quot;Municipality&quot; column in your CSV file</li>
                        <li>• Remove or correct any entries from other municipalities</li>
                        <li>• Upload only data for your municipality: <strong>{userMunicipality}</strong></li>
                      </ul>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter className="border-t border-gray-200 pt-4">
                  <Button
                    color="primary"
                    onPress={onClose}
                    className="bg-gradient-to-r from-[#143D60] to-[#1e4a6b] text-white font-semibold"
                  >
                    I Understand
                  </Button>
                </ModalFooter>
              </>
            )}
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
